{"@platforms": ["android", "iPhone", "iPad"], "id": "__W2A__jg.qinye.cool", "name": "酒馆", "version": {"name": "1.0", "code": ""}, "description": "", "icons": {"72": "icon.png"}, "launch_path": "https://jg.qinye.cool", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"Accelerometer": {}, "Audio": {}, "Messaging": {}, "Cache": {"description": "管理应用缓存"}, "Camera": {}, "Console": {"description": "跟踪调试输出日志"}, "Contacts": {}, "Device": {}, "Downloader": {}, "Events": {"description": "应用扩展事件"}, "File": {}, "Gallery": {}, "Geolocation": {}, "Invocation": {}, "Orientation": {}, "Proximity": {}, "Storage": {}, "Uploader": {}, "Runtime": {}, "XMLHttpRequest": {}, "Zip": {}, "Barcode": {}, "Maps": {}, "Speech": {}, "Webview": {}, "NativeUI": {}, "Navigator": {}, "NativeObj": {}, "Push": {}, "Share": {}, "OAuth": {}, "Payment": {}, "Statistic": {}, "UIWebview": {}}, "plus": {"kernel": {"ios": "UIWebview"}, "launchwebview": {"contentAdjust": false, "statusbarKey": {"background": "#f7f7f7"}}, "splashscreen": {"autoclose": true, "waiting": true}, "statusbar": {"immersed": "supportedDevice", "style": "dark"}, "popGesture": "close", "runmode": "liberate", "signature": "********************************************", "secondwebview": {"mode": "behind", "launch_path": "_www/client_index.html", "id": "__W2A_CONTEXT_"}}, "screenOrientation": ["portrait-primary"], "fullscreen": false}