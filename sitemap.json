{
    "global": {
        "webviewParameter": {
            "titleNView": {
                "autoBackButton": true,
                "backgroundColor": "#f7f7f7",//导航栏背景色
                "titleColor": "#000000",//标题颜色
                "titleSize": "17px"
            },
            "statusbar": {
                //系统状态栏样式(前景色)
                "style": "dark"
            },
            "appendCss": "",
            "appendJs": ""
        },
        "easyConfig": {}
    },
    "pages": [
        {
            "webviewId": "__W2A__jg.qinye.cool",//首页
            "matchUrls": [
                {
                    "href": "https://jg.qinye.cool"
                }, {
                    "href": "https://jg.qinye.cool/"
                }
            ],
            "webviewParameter": {
                "titleNView": false,
                "statusbar": {
                    //状态条背景色，
                    //首页不使用原生导航条，颜色值建议和global->webviewParameter->titleNView->backgroundColor颜色值保持一致
                    //若首页启用了原生导航条，则建议将首页的statusbar配置为false，这样状态条可以和原生导航条背景色保持一致；
                    "background": "#f7f7f7"
                }
            }
        }
    ]
}