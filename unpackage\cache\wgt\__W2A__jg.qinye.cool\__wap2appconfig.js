!(function() {
    wap2app.appid = '__W2A__jg.qinye.cool';
    var define = wap2app.define;
    var require = wap2app.require;
    var App = wap2app.App;
    var Page = wap2app.Page;
    var __w2aRoute;
    /************************sitemap begin************************************/
    /*暂时先集成进去兼容旧版本基座*/
    window.__wap2app_sitemap = {"global":{"webviewParameter":{"titleNView":{"autoBackButton":true,"backgroundColor":"#f7f7f7","titleColor":"#000000","titleSize":"17px"},"statusbar":{"style":"dark"},"appendCss":"","appendJs":""},"easyConfig":{}},"pages":[{"webviewId":"__W2A__jg.qinye.cool","matchUrls":[{"href":["^https\\:\\/\\/jg\\.qinye\\.cool$"]},{"href":["^https\\:\\/\\/jg\\.qinye\\.cool\\/$"]}],"webviewParameter":{"titleNView":false,"statusbar":{"background":"#f7f7f7"},"appendCss":""}}]};
    /************************sitemap end**************************************/

    /************************util begin***************************************/
    
    /************************util end*****************************************/
    /************************nviews begin*************************************/
    
    /************************nviews end***************************************/
    /************************pages begin**************************************/
    define('app.js', function (require, module) {
    App({
    options: {
        debug: false
    },
    onLaunch: function() {
        console.log('launch');
        // 检查认证状态
        this.checkAuth();
    },
    onShow: function() {
        console.log('show');
    },
    onHide: function() {
        console.log('hide');
    },
    
    // 检查认证状态
    checkAuth: function() {
        var authInfo = plus.storage.getItem('authInfo');
        if (!authInfo) {
            this.showLoginDialog();
        } else {
            // 如果有认证信息，设置全局请求头
            this.setAuthHeaders(JSON.parse(authInfo));
        }
    },
    
    // 显示登录弹窗
    showLoginDialog: function() {
        var self = this;
        plus.nativeUI.prompt('请输入用户名', function(e) {
            if (e.index == 0 && e.value) {
                var username = e.value;
                plus.nativeUI.prompt('请输入密码', function(e2) {
                    if (e2.index == 0 && e2.value) {
                        var password = e2.value;
                        self.doLogin(username, password);
                    } else {
                        // 用户取消或未输入密码，重新显示登录
                        setTimeout(function() {
                            self.showLoginDialog();
                        }, 500);
                    }
                }, '密码', ['确定', '取消'], '', 'password');
            } else {
                // 用户取消或未输入用户名，重新显示登录
                setTimeout(function() {
                    self.showLoginDialog();
                }, 500);
            }
        }, '用户名', ['确定', '取消']);
    },
    
    // 执行登录
    doLogin: function(username, password) {
        var self = this;
        var authInfo = {
            username: username,
            password: password,
            timestamp: Date.now()
        };
        
        // 保存认证信息
        plus.storage.setItem('authInfo', JSON.stringify(authInfo));
        
        // 设置认证头
        this.setAuthHeaders(authInfo);
        
        // 显示登录成功提示
        plus.nativeUI.toast('登录成功');
        
        // 重新加载主页面
        var mainWebview = plus.webview.getWebviewById('__W2A__jg.qinye.cool');
        if (mainWebview) {
            mainWebview.reload();
        }
    },
    
    // 设置认证请求头
    setAuthHeaders: function(authInfo) {
        // 创建Basic Auth字符串
        var basicAuth = 'Basic ' + btoa(authInfo.username + ':' + authInfo.password);
        
        // 设置全局请求头
        if (plus.navigator && plus.navigator.setUserAgent) {
            // 通过UserAgent传递认证信息（某些情况下有效）
            var currentUA = plus.navigator.getUserAgent();
            plus.navigator.setUserAgent(currentUA + '; Auth=' + basicAuth);
        }
        
        // 尝试设置全局HTTP头（如果支持）
        if (plus.net && plus.net.setHTTPHeader) {
            plus.net.setHTTPHeader('Authorization', basicAuth);
        }
    },
    
    // 清除认证信息（可用于退出登录）
    clearAuth: function() {
        plus.storage.removeItem('authInfo');
        plus.nativeUI.toast('已退出登录');
    }
});

Page('__W2A__jg.qinye.cool', {
    onShow: function() {
        // 页面显示时检查认证状态
        var authInfo = plus.storage.getItem('authInfo');
        if (authInfo) {
            var auth = JSON.parse(authInfo);
            // 延迟注入认证信息，确保页面完全加载
            setTimeout(() => {
                this.injectAuth(auth);
            }, 1000);
        }
    },
    onClose: function() {

    },
    
    // 在页面中注入认证信息
    injectAuth: function(authInfo) {
        var script = `
            (function() {
                console.log('注入认证信息开始');
                
                // 拦截XMLHttpRequest
                var originalXHR = window.XMLHttpRequest;
                window.XMLHttpRequest = function() {
                    var xhr = new originalXHR();
                    var originalOpen = xhr.open;
                    var originalSend = xhr.send;
                    
                    xhr.open = function(method, url, async, user, password) {
                        console.log('XHR请求:', method, url);
                        originalOpen.call(this, method, url, async, user, password);
                        // 添加Basic Auth头
                        this.setRequestHeader('Authorization', 'Basic ' + btoa('${authInfo.username}:${authInfo.password}'));
                        console.log('已添加Authorization头');
                    };
                    
                    return xhr;
                };
                
                // 拦截fetch请求
                var originalFetch = window.fetch;
                window.fetch = function(url, options) {
                    console.log('Fetch请求:', url);
                    options = options || {};
                    options.headers = options.headers || {};
                    options.headers['Authorization'] = 'Basic ' + btoa('${authInfo.username}:${authInfo.password}');
                    console.log('已添加Authorization头到fetch');
                    return originalFetch(url, options);
                };
                
                // 拦截页面加载请求
                var originalLocation = window.location;
                Object.defineProperty(window, 'location', {
                    get: function() {
                        return originalLocation;
                    },
                    set: function(url) {
                        // 如果是同域请求，添加认证头
                        if (typeof url === 'string' && url.indexOf('jg.qinye.cool') !== -1) {
                            var xhr = new XMLHttpRequest();
                            xhr.open('GET', url);
                            xhr.setRequestHeader('Authorization', 'Basic ' + btoa('${authInfo.username}:${authInfo.password}'));
                            xhr.onload = function() {
                                document.open();
                                document.write(xhr.responseText);
                                document.close();
                            };
                            xhr.send();
                        } else {
                            originalLocation = url;
                        }
                    }
                });
                
                console.log('认证信息注入完成');
            })();
        `;
        
        // 注入脚本到页面
        plus.webview.currentWebview().evalJS(script);
        
        // 同时尝试重新加载页面并添加认证头
        this.reloadWithAuth(authInfo);
    },
    
    // 带认证信息重新加载页面
    reloadWithAuth: function(authInfo) {
        var webview = plus.webview.currentWebview();
        var basicAuth = 'Basic ' + btoa(authInfo.username + ':' + authInfo.password);
        
        // 尝试通过webview的方式添加请求头
        webview.loadURL('https://jg.qinye.cool', {
            headers: {
                'Authorization': basicAuth
            }
        });
    }
});

});
require("app.js");
    /************************pages end****************************************/

    wap2app.initSitemap();
})();